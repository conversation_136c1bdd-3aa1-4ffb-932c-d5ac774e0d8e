
setwd("~/桌面/cleandata")

library(openxlsx)
rawdata<-read.xlsx("CPTAC_withinfo.xlsx")

# 查看原始数据
print("原始 AJCC.Pathologic.Stage 数据:")
print(unique(rawdata[['AJCC.Pathologic.Stage']]))

# 使用正则表达式处理 AJCC.Pathologic.Stage 列
# 方法1: 使用 gsub 和正则表达式
rawdata$Stage_processed_v1 <- gsub("^Stage\\s+([IVX]+[ABC0-9]*).*$", "Stage \\1", rawdata[['AJCC.Pathologic.Stage']])

# 方法2: 更精确的正则表达式处理
rawdata$Stage_processed_v2 <- ifelse(
  grepl("^Stage\\s+", rawdata[['AJCC.Pathologic.Stage']]),
  gsub("^Stage\\s+([IVX]+[ABC0-9]*).*$", "Stage \\1", rawdata[['AJCC.Pathologic.Stage']]),
  rawdata[['AJCC.Pathologic.Stage']]  # 保持 "Not Reported" 等非标准格式不变
)

# 方法3: 使用 stringr 包的更现代化方法
library(stringr)
rawdata$Stage_processed_v3 <- str_replace(rawdata[['AJCC.Pathologic.Stage']],
                                          "^Stage\\s+([IVX]+[ABC0-9]*).*$",
                                          "Stage \\1")

# 查看处理结果
print("\n处理后的结果对比:")
comparison <- data.frame(
  Original = rawdata[['AJCC.Pathologic.Stage']],
  Method1 = rawdata$Stage_processed_v1,
  Method2 = rawdata$Stage_processed_v2,
  Method3 = rawdata$Stage_processed_v3
)
print(unique(comparison))
