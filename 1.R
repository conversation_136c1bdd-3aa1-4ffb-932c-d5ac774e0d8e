
setwd("~/桌面/cleandata")

library(openxlsx)
rawdata<-read.xlsx("CPTAC_withinfo.xlsx")


rawdata$AJCC_Pathologic_Stage_processed <- gsub("^Stage\\s+([IVX]+).*$", "Stage \\1", rawdata[['AJCC.Pathologic.Stage']])





# 处理 AJCC.Pathologic.T 列，只保留主要T分期（T1, T2, T3, T4）
rawdata$AJCC_Pathologic_T_processed <- ifelse(
  grepl("^T[0-9]", rawdata[['AJCC.Pathologic.T']]),
  gsub("^T([0-9]+).*$", "T\\1", rawdata[['AJCC.Pathologic.T']]),
  rawdata[['AJCC.Pathologic.T']]  # 保持空值或其他格式不变
)





# 批量提取函数：提取所有分期的TRMT10C数据
extract_all_stages_trmt10c <- function(data, stage_column, trmt10c_column = "TRMT10C") {
 
  #批量提取所有分期的TRMT10C表达数据

  #参数:
  #data: 数据框
  #stage_column: 分期列名（字符串）
  #trmt10c_column: TRMT10C列名（字符串，默认为"TRMT10C"）

  #返回:
  #包含所有分期和对应TRMT10C表达数据的数据框
  

  # 获取所有唯一的分期值
  unique_stages <- unique(data[[stage_column]])
  unique_stages <- unique_stages[!is.na(unique_stages) & unique_stages != ""]

  # 初始化结果列表
  all_results <- list()

  # 循环提取每个分期的数据
  for (stage in unique_stages) {
    stage_data <- extract_stage_trmt10c(data, stage_column, stage, trmt10c_column)
    if (nrow(stage_data) > 0) {
      all_results[[stage]] <- stage_data
    }
  }

  # 合并所有结果
  if (length(all_results) > 0) {
    final_result <- do.call(rbind, all_results)
    rownames(final_result) <- NULL
    return(final_result)
  } else {
    return(data.frame())
  }
}





all_t_data <- extract_all_stages_trmt10c(rawdata, "AJCC_Pathologic_T_processed")
print(head(all_t_data, 10))
#"各T分期样本数量统计:
print(table(all_t_data$Stage))


# 获取所有唯一的Stage分期
unique_stages <- unique(rawdata$AJCC_Pathologic_T_processed)
unique_stages <- unique_stages[!is.na(unique_stages) & unique_stages != ""]

# 为每个分期创建单独的列
for (stage in unique_stages) {
  # 创建列名
  col_name <- paste0(gsub(" ", "_", stage))

  # 初始化列为NA
  rawdata[[col_name]] <- NA

  # 填入对应分期的TRMT10C数据
  stage_indices <- which(rawdata$AJCC_Pathologic_T_processed == stage)
  rawdata[[col_name]][stage_indices] <- rawdata$TRMT10C[stage_indices]

  print(paste("创建列:", col_name, "- 样本数量:", length(stage_indices)))
}

# 显示新创建的列
new_cols <- colnames(rawdata)[grepl("TRMT10C_Stage", colnames(rawdata))]
print("新创建的分期特异性TRMT10C列:")
print(new_cols)
